package com.yunqu.park.iot.model.command.params;

import lombok.Data;

/**
 * 设置震动报警参数（兼容指令）
 * 对应VIBRATION,X,M#指令
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
public class SetVibrationLegacyParams {
    
    /**
     * 报警级别 (0-5)
     * 0为关闭报警，1到5级，数字越低越灵敏
     */
    private Integer level;
    
    /**
     * 报警方式 (0-4)
     * 0：仅GPRS；1：电话+GPRS；2：短信+GPRS；3：电话+短信+GPRS；4：电话+GPRS
     */
    private Integer mode;
}
