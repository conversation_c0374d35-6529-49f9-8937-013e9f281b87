package com.yunqu.park.iot.netty.codec;

import com.yunqu.park.iot.netty.protocol.GpsData;
import com.yunqu.park.iot.netty.protocol.IotMessage;
import com.yunqu.park.iot.netty.protocol.LbsData;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.embedded.EmbeddedChannel;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GT06协议0x12定位数据包里程数据测试
 */
public class GT06MileageTest {

    private static final Logger log = LoggerFactory.getLogger(GT06MileageTest.class);

    /**
     * 测试标准0x12数据包（26字节内容，不含里程）
     */
    @Test
    public void testStandardLocationPacket() {
        // 构造标准0x12数据包（不含里程）
        byte[] packetData = {
            0x78, 0x78,                                    // 起始位
            0x1A,                                          // 包长度 (26字节)
            0x12,                                          // 协议号
            // GPS数据 (18字节)
            0x13, 0x08, 0x1D, 0x11, 0x0C, 0x10,          // 日期时间
            (byte) 0xCB,                                   // 卫星数
            0x02, 0x7A, (byte) 0xCF, (byte) 0xEB,        // 纬度
            0x0C, 0x46, 0x58, 0x49,                       // 经度
            0x10,                                          // 速度
            0x15, 0x40,                                    // 航向、状态
            // LBS数据 (8字节)
            0x01, 0x00,                                    // MCC
            0x00,                                          // MNC
            0x28, 0x7D,                                    // LAC
            0x00, 0x1F, (byte) 0xB8,                      // Cell ID
            0x00, 0x03,                                    // 序列号
            (byte) 0x9D, (byte) 0xDC,                     // CRC
            0x0D, 0x0A                                     // 停止位
        };

        ByteBuf buffer = Unpooled.wrappedBuffer(packetData);
        EmbeddedChannel channel = new EmbeddedChannel(new GT06ProtocolDecoder());

        // 解码
        channel.writeInbound(buffer);
        IotMessage message = channel.readInbound();

        // 验证
        assertNotNull(message);
        assertEquals(0x12, message.getProtocol());
        
        GpsData gpsData = message.getGpsData();
        assertNotNull(gpsData);
        assertNull(gpsData.getMileage()); // 标准包不应该有里程数据
        
        LbsData lbsData = message.getLbsData();
        assertNotNull(lbsData);
        assertEquals(256, lbsData.getMcc());
        assertEquals(0, lbsData.getMnc());

        log.info("标准0x12数据包测试通过: 里程={}", gpsData.getMileage());
    }

    /**
     * 测试含里程0x12数据包（30字节内容）
     */
    @Test
    public void testLocationPacketWithMileage() {
        // 构造含里程的0x12数据包
        byte[] packetData = {
            0x78, 0x78,                                    // 起始位
            0x23,                                          // 包长度 (35字节)
            0x12,                                          // 协议号
            // GPS数据 (18字节)
            0x13, 0x08, 0x1D, 0x11, 0x0C, 0x10,          // 日期时间
            (byte) 0xCB,                                   // 卫星数
            0x02, 0x7A, (byte) 0xCF, (byte) 0xEB,        // 纬度
            0x0C, 0x46, 0x58, 0x49,                       // 经度
            0x10,                                          // 速度
            0x15, 0x40,                                    // 航向、状态
            // LBS数据 (8字节)
            0x01, 0x00,                                    // MCC
            0x00,                                          // MNC
            0x28, 0x7D,                                    // LAC
            0x00, 0x1F, (byte) 0xB8,                      // Cell ID
            // 里程数据 (4字节) - 示例：1000000米 = 0x000F4240
            0x00, 0x0F, 0x42, 0x40,                       // 里程
            0x00, 0x03,                                    // 序列号
            (byte) 0x9D, (byte) 0xDC,                     // CRC
            0x0D, 0x0A                                     // 停止位
        };

        ByteBuf buffer = Unpooled.wrappedBuffer(packetData);
        EmbeddedChannel channel = new EmbeddedChannel(new GT06ProtocolDecoder());

        // 解码
        channel.writeInbound(buffer);
        IotMessage message = channel.readInbound();

        // 验证
        assertNotNull(message);
        assertEquals(0x12, message.getProtocol());
        
        GpsData gpsData = message.getGpsData();
        assertNotNull(gpsData);
        assertNotNull(gpsData.getMileage()); // 应该有里程数据
        assertEquals(1000000L, gpsData.getMileage().longValue()); // 验证里程值
        
        LbsData lbsData = message.getLbsData();
        assertNotNull(lbsData);
        assertEquals(256, lbsData.getMcc());
        assertEquals(0, lbsData.getMnc());

        log.info("含里程0x12数据包测试通过: 里程={}米", gpsData.getMileage());
    }
}
