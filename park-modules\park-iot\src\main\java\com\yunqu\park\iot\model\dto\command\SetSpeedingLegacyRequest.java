package com.yunqu.park.iot.model.dto.command;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置超速报警请求（兼容指令）
 * 兼容旧版SPEEDING,速度,报警方式#指令格式
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetSpeedingLegacyRequest extends BaseCommandReq {
    
    /**
     * 速度设置 (60-220 km/h)
     * 当值不在此范围内，则为关闭报警
     */
    @NotNull(message = "速度设置不能为空")
    @Min(value = 0, message = "速度设置不能小于0")
    @Max(value = 300, message = "速度设置不能大于300")
    private Integer speed;
    
    /**
     * 报警方式 (0-3)
     * 0：仅GPRS；1：电话+GPRS；2：短信+GPRS；3：电话+短信+GPRS
     */
    @NotNull(message = "报警方式不能为空")
    @Min(value = 0, message = "报警方式不能小于0")
    @Max(value = 3, message = "报警方式不能大于3")
    private Integer mode;
}
