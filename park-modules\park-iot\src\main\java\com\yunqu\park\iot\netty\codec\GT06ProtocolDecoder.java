package com.yunqu.park.iot.netty.codec;

import com.yunqu.park.common.core.utils.SpringUtils;
import com.yunqu.park.iot.config.IotProtocolConfig;
import com.yunqu.park.iot.constant.IotConstants;
import com.yunqu.park.iot.netty.protocol.*;
import com.yunqu.park.iot.pool.IotMessagePool;
import com.yunqu.park.iot.utils.ByteBufUtils;
import com.yunqu.park.iot.utils.CrcUtils;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import lombok.extern.slf4j.Slf4j;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * GT06协议解码器 - 修复版本
 *
 * <AUTHOR>
 */
@Slf4j
public class GT06ProtocolDecoder extends ByteToMessageDecoder {

    @Override
    public void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
        // 记录原始数据用于调试
        int originalReadableBytes = in.readableBytes();
        log.info("[RAW-DATA] {} Length={}", "IN", originalReadableBytes);

        // 在解码前打印接收到的完整原始数据（不影响后续解码）
        if (originalReadableBytes > 0) {
            // 安全地复制数据用于调试输出
            try {
                ByteBuf copy = in.copy();
                byte[] rawData = new byte[copy.readableBytes()];
                copy.readBytes(rawData);

                // 无论日志级别如何，都输出原始数据的十六进制表示
                log.info("[RAW-TCP-IN] 收到来自[{}]的原始数据: {} 字节\n原始十六进制: {}",
                    ctx.channel().remoteAddress(),
                    rawData.length,
                    ByteBufUtils.bytesToHexString(rawData));

                // 释放复制的ByteBuf
                copy.release();
            } catch (Exception e) {
                log.warn("[RAW-TCP-IN] 原始数据输出异常: {}", e.getMessage());
            }
        }

        if (originalReadableBytes < IotConstants.GT06Protocol.MIN_PACKET_LENGTH) {
            log.debug("[PROTOCOL-DECODE] 数据包不完整，等待更多数据: available={}, required={}",
                originalReadableBytes, IotConstants.GT06Protocol.MIN_PACKET_LENGTH);
            return; // 不抛异常，继续等待更多数据
        }

        // 增强容错：检查是否是9字节的异常数据包 - 暂时禁用
        // if (originalReadableBytes == 9) {
        //     log.debug("[PROTOCOL-DECODE] 检测到9字节数据包，尝试修复处理");
        //     ByteBuf repaired = repairIncompletePacket(in);
        //     if (repaired != null) {
        //         log.debug("[PROTOCOL-DECODE] 数据包修复成功，使用修复后的数据");
        //         // 使用修复后的数据继续处理
        //         in.clear();
        //         in.writeBytes(repaired);
        //         repaired.release();
        //     }
        // }

        in.markReaderIndex();

        try {
            log.debug("[PROTOCOL-DECODE] 🔍 开始解码数据包: RemoteAddress={}, AvailableBytes={}",
                ctx.channel().remoteAddress(), originalReadableBytes);

            // 1. 检查起始位 (支持0x7878和0x7979)
            byte[] startFlag = ByteBufUtils.safeReadBytes(in, 2);
            if (startFlag == null) {
                in.resetReaderIndex();
                log.debug("[PROTOCOL-DECODE] 数据不足，无法读取起始位，等待更多数据");
                return;
            }

            if (!Arrays.equals(startFlag, IotConstants.GT06Protocol.START_FLAG_7878) &&
                !Arrays.equals(startFlag, IotConstants.GT06Protocol.START_FLAG_7979)) {
                in.resetReaderIndex();
                in.skipBytes(1); // 跳过一个字节继续寻找
                log.debug("[PROTOCOL-DECODE] 无效起始位: {}, 跳过1字节继续寻找",
                    ByteBufUtils.bytesToHexString(startFlag));
                return;
            }

            log.debug("[PROTOCOL-DECODE] ✅ 起始位验证通过: {}", ByteBufUtils.bytesToHexString(startFlag));

            // 2. 读取包长度
            Byte packetLengthByte = ByteBufUtils.safeReadByte(in);
            if (packetLengthByte == null) {
                in.resetReaderIndex();
                log.debug("[PROTOCOL-DECODE] 数据不足，无法读取包长度字节");
                return;
            }

            byte packetLength = packetLengthByte;
            int packetLengthValue = packetLength & 0xFF;
            log.debug("[PROTOCOL-DECODE] 📏 包长度字段值: {}, 剩余可读字节: {}",
                packetLengthValue, in.readableBytes());

            // 3. 修复：正确验证数据完整性
            int remainingDataLength = packetLengthValue;

            // 增强边界检查：验证包长度的合理性
            if (remainingDataLength < 4 || remainingDataLength > 1024) {
                in.resetReaderIndex();
                in.skipBytes(1); // 跳过一个字节继续寻找
                log.warn("[PROTOCOL-DECODE] 包长度异常: {}, 跳过1字节继续寻找", remainingDataLength);
                return;
            }

            // 验证数据完整性
            if (in.readableBytes() < remainingDataLength + 2) { // +2 for stop flag
                in.resetReaderIndex();
                log.debug("[PROTOCOL-DECODE] 数据包不完整: 需要{}字节(数据{}+停止位2), 可用{}字节, 等待更多数据",
                    remainingDataLength + 2, remainingDataLength, in.readableBytes());
                return;
            }

            log.debug("[PROTOCOL-DECODE] ✅ 数据完整性验证通过: 需要{}字节, 可用{}字节",
                remainingDataLength + 2, in.readableBytes());

            // 4. 修复：一次性读取所有数据（不包含停止位）
            byte[] packetData = ByteBufUtils.safeReadBytes(in, remainingDataLength);
            if (packetData == null) {
                in.resetReaderIndex();
                log.warn("[PROTOCOL-DECODE] ❌ 无法读取数据包内容: 需要{}字节, 可用{}字节",
                    remainingDataLength, in.readableBytes());
                return;
            }

            log.debug("[PROTOCOL-DECODE] ✅ 数据包内容读取成功: {}字节, 数据={}, 第一个字节(协议号)=0x{}",
                packetData.length, ByteBufUtils.bytesToHexString(packetData),
                String.format("%02X", packetData[0] & 0xFF));

            // 5. 修复：验证停止位（不抛异常，改为跳过处理）
            byte[] stopFlag = ByteBufUtils.safeReadBytes(in, 2);
            if (stopFlag == null) {
                in.resetReaderIndex();
                log.warn("[PROTOCOL-DECODE] ❌ 无法读取停止位: 可用{}字节", in.readableBytes());
                return;
            }

            if (!Arrays.equals(stopFlag, IotConstants.GT06Protocol.STOP_FLAG)) {
                log.warn("[PROTOCOL-DECODE] ⚠️ 停止位不匹配: 期望={}, 实际={}, 跳过此数据包",
                    ByteBufUtils.bytesToHexString(IotConstants.GT06Protocol.STOP_FLAG),
                    ByteBufUtils.bytesToHexString(stopFlag));
                // 不抛异常，跳过这个字节继续处理
                in.resetReaderIndex();
                in.skipBytes(1);
                return;
            }

            log.debug("[PROTOCOL-DECODE] ✅ 停止位验证通过: {}", ByteBufUtils.bytesToHexString(stopFlag));

            // 6. 修复：CRC校验（宽松模式，失败不中断处理）
            boolean crcValid = performCrcValidation(packetData, ctx);
            log.debug("[PROTOCOL-DECODE] CRC校验结果: {}", crcValid ? "通过" : "失败(继续处理)");

            // 7. 解析协议内容并构造消息对象
            IotMessage message = parseMessage(startFlag, packetData, ctx);
            if (message != null) {
                out.add(message);
                log.info("[PROTOCOL-DECODE] ✅ 数据包解码成功: Protocol=0x{}, IMEI={}, SequenceNumber={}, RemoteAddress={}",
                    String.format("%02X", message.getProtocol() & 0xFF),
                    message.getImei(), message.getSequenceNumber(), ctx.channel().remoteAddress());

            } else {
                log.warn("[PROTOCOL-DECODE] ❌ 消息解析失败: RemoteAddress={}, 数据={}",
                    ctx.channel().remoteAddress(), ByteBufUtils.bytesToHexString(packetData));
            }

        } catch (Exception e) {
            log.error("[PROTOCOL-DECODE] ❌ 解码过程异常: RemoteAddress={}, Error={}, OriginalBytes={}, CurrentBytes={}",
                ctx.channel().remoteAddress(), e.getMessage(), originalReadableBytes, in.readableBytes(), e);

            // 增强异常处理：清空缓冲区，避免影响后续数据包
            try {
                in.clear();
                log.debug("[PROTOCOL-DECODE] 清空缓冲区，避免异常数据包影响后续处理");
            } catch (Exception clearException) {
                log.warn("[PROTOCOL-DECODE] 清空缓冲区时异常: {}", clearException.getMessage());
            }
        }
    }


    /**
     * 解析消息内容
     */
    private IotMessage parseMessage(byte[] startFlag, byte[] packetData, ChannelHandlerContext ctx) {
        if (packetData.length < 4) {
            log.warn("[PROTOCOL-DECODE] 数据包太短，无法解析: 长度={}", packetData.length);
            return null;
        }

        try {
            IotMessage message = new IotMessage();
            message.setStartFlag(startFlag);

            // 协议号是packetData的第一个字节
            byte protocol = packetData[0];
            message.setProtocol(protocol);

            // 内容从packetData的第二个字节开始
            int contentLength = packetData.length - 4; // 减去协议号(1) + 序列号(2) + CRC(2)
            if (contentLength > 0) {
                byte[] content = new byte[contentLength];
                System.arraycopy(packetData, 1, content, 0, contentLength);
                message.setContent(content);

                // 根据不同协议解析内容 - 按照协议文档标准进行解析
                switch (protocol) {
                    case IotConstants.GT06Protocol.PROTOCOL_LOGIN:
                        // 0x01 - 登录包
                        if (contentLength >= 8) {
                            String imei = parseImeiFromLoginPacket(content);
                            message.setImei(imei);
                            message.setNeedResponse(true);
                        }
                        break;
                    case IotConstants.GT06Protocol.PROTOCOL_LOCATION:
                        // 0x12 - 定位数据包（GPS/LBS合并，支持标准26字节和含里程30字节）
                        if (contentLength >= 15) {
                            parseLocationData(message, content);
                        } else {
                            log.warn("[PROTOCOL-DECODE] 定位数据包内容长度不足: {}, 最小需要15字节", contentLength);
                        }
                        break;
                    case IotConstants.GT06Protocol.PROTOCOL_STATUS_INFO:
                        // 0x13 - 状态信息包（心跳包）
                        if (contentLength >= 1) {
                            StatusData statusData = StatusData.from(content[0]);
                            message.setStatusData(statusData);
                            message.setNeedResponse(true);
                        }
                        break;
                    case IotConstants.GT06Protocol.PROTOCOL_ALARM_INFO:
                        // 0x16 - 报警信息
                        if (contentLength >= 1) {
                            AlarmData alarmData = AlarmData.from(content[0]);
                            message.setAlarmData(alarmData);
                            log.info("[PROTOCOL-DECODE] 解析报警信息: 类型=0x{}, 描述={}",
                                String.format("%02X", alarmData.getType()),
                                alarmData.getTypeDescription());
                        }
                        break;
                    case IotConstants.GT06Protocol.PROTOCOL_LBS:
                        // 0x18 - LBS多基站信息
                        parseLbsMultiPacket(message, content);
                        break;
                    case IotConstants.GT06Protocol.PROTOCOL_ADDRESS_QUERY:
                        // 0x1A - 查询地址信息（GPS）
                        parseAddressQueryPacket(message, content);
                        break;
                    case IotConstants.GT06Protocol.PROTOCOL_LBS_WIFI:
                        // 0x2C - LBS+WIFI信息
                        parseLbsWifiPacket(message, content);
                        break;
                    case IotConstants.GT06Protocol.PROTOCOL_SERVER_CMD:
                        // 0x80 - 下发指令
                        parseServerCommandPacket(message, content);
                        break;
                    case IotConstants.GT06Protocol.PROTOCOL_RECORD:
                        // 0x8D - 录音文件上报平台信息
                        parseRecordPacket(message, content);
                        message.setNeedResponse(true);
                        break;
                    case IotConstants.GT06Protocol.PROTOCOL_IMSI:
                        // 0x90 - IMSI号上报平台信息
                        parseImsiPacket(message, content);
                        break;
                    case IotConstants.GT06Protocol.PROTOCOL_ICCID:
                        // 0x94 - ICCID号上报平台信息
                        parseIccidPacket(message, content);
                        break;
                    default:
                        log.debug("[PROTOCOL-DECODE] 接收到未处理协议: 0x{}", String.format("%02X", protocol & 0xFF));
                        break;
                }
            }

            // 解析序列号（大端序）
            int sequenceNumber = ((packetData[packetData.length - 4] & 0xFF) << 8) |
                (packetData[packetData.length - 3] & 0xFF);
            message.setSequenceNumber(sequenceNumber);

            // 解析CRC（大端序）
            int crc = ((packetData[packetData.length - 2] & 0xFF) << 8) |
                (packetData[packetData.length - 1] & 0xFF);
            message.setCrc(crc);

            message.setStopFlag(IotConstants.GT06Protocol.STOP_FLAG);

            // 设置客户端信息
            String remoteAddress = ctx.channel().remoteAddress().toString();
            if (remoteAddress.startsWith("/")) {
                remoteAddress = remoteAddress.substring(1);
            }
            String[] parts = remoteAddress.split(":");
            message.setClientIp(parts[0]);
            if (parts.length > 1) {
                try {
                    message.setClientPort(Integer.parseInt(parts[1]));
                } catch (NumberFormatException e) {
                    message.setClientPort(0);
                }
            }

            return message;

        } catch (Exception e) {
            log.error("[PROTOCOL-DECODE] 消息解析异常: {}, 数据={}",
                e.getMessage(), ByteBufUtils.bytesToHexString(packetData), e);
            return null;
        }
    }

    /**
     * 从登录包中解析IMEI
     * 修复版本：正确处理BCD编码的IMEI
     * 第一个字节只取低4位，后续字节正常解析高低4位
     */
    private String parseImeiFromLoginPacket(byte[] content) {
        if (content.length < 8) {
            log.warn("[PROTOCOL-DECODE] 登录包IMEI数据长度不足: {}", content.length);
            return null;
        }

        try {
            StringBuilder imei = new StringBuilder();

            // 第一个字节只取低4位 (忽略高4位的填充)
            imei.append(content[0] & 0x0F);

            // 后续7个字节正常解析高4位和低4位
            for (int i = 1; i < 8; i++) {
                int high = (content[i] >> 4) & 0x0F;
                int low = content[i] & 0x0F;
                imei.append(high).append(low);
            }

            String result = imei.toString();

            // 验证IMEI长度 (标准IMEI应该是15位)
            if (result.length() != 15) {
                log.warn("[PROTOCOL-DECODE] 解析出的IMEI长度异常: {} (期望15位)", result.length());
            }

            log.debug("[PROTOCOL-DECODE] 成功解析IMEI: {} (原始数据: {})",
                     result, bytesToHexString(content, 0, 8));

            return result;

        } catch (Exception e) {
            log.error("[PROTOCOL-DECODE] 解析IMEI异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 字节数组转十六进制字符串 (用于调试日志)
     */
    private String bytesToHexString(byte[] bytes, int start, int length) {
        if (bytes == null || start < 0 || start + length > bytes.length) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (int i = start; i < start + length; i++) {
            if (i > start) sb.append(" ");
            sb.append(String.format("%02X", bytes[i] & 0xFF));
        }
        return sb.toString();
    }

    /**
     * 执行CRC校验
     */
    private boolean performCrcValidation(byte[] packetData, ChannelHandlerContext ctx) {
        try {
            IotProtocolConfig config = getProtocolConfig();

            if (!config.getCrc().isEnabled()) {
                log.debug("[PROTOCOL-DECODE] CRC校验已禁用");
                return true;
            }

            boolean crcValid = CrcUtils.validateCrcLenient(packetData);

            if (!crcValid) {
                log.warn("[PROTOCOL-DECODE] ⚠️ CRC校验失败，但继续处理数据包 (宽松模式)");
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("[PROTOCOL-DECODE] CRC校验异常: {}, 继续处理数据包", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取协议配置
     */
    private IotProtocolConfig getProtocolConfig() {
        try {
            return SpringUtils.getBean(IotProtocolConfig.class);
        } catch (Exception e) {
            IotProtocolConfig defaultConfig = new IotProtocolConfig();
            defaultConfig.getCrc().setEnabled(true);
            defaultConfig.getCrc().setLenientMode(true);
            return defaultConfig;
        }
    }

    /**
     * 解析坐标数据 (静态方法，供其他类调用)
     * 修复版本：正确处理GT06协议中以"分"为单位存储的坐标数据
     *
     * @param bytes 4字节坐标数据
     * @return 解析后的坐标值（度数）
     */
    public static double parseCoordinate(byte[] bytes) {
        if (bytes == null || bytes.length != 4) {
            log.warn("[COORDINATE-PARSE] 坐标数据长度无效: {}", bytes != null ? bytes.length : 0);
            return 0.0;
        }

        try {
            // 将4字节转换为int (大端序)
            int value = ((bytes[0] & 0xFF) << 24) |
                        ((bytes[1] & 0xFF) << 16) |
                        ((bytes[2] & 0xFF) << 8) |
                        (bytes[3] & 0xFF);

            // GT06协议中坐标以"分"为单位存储
            // 转换步骤：原始值 → 分 → 度
            // 1. 除以30000得到分 (minutes)
            // 2. 除以60转换为度 (degrees)
            // 合并：除以(30000 * 60) = 1800000
            double degrees = value / 1800000.0;

            log.debug("[COORDINATE-PARSE] 坐标解析: 原始值={}, 度数={}", value, degrees);

            return degrees;

        } catch (Exception e) {
            log.error("[COORDINATE-PARSE] 坐标解析异常: {}", e.getMessage(), e);
            return 0.0;
        }
    }

    /**
     * 解析坐标数据 (旧版本，保留用于兼容性测试)
     * @deprecated 使用修复后的parseCoordinate方法
     */
    @Deprecated
    public static double parseCoordinateOldWay(byte[] bytes) {
        if (bytes == null || bytes.length != 4) {
            return 0.0;
        }

        int value = ((bytes[0] & 0xFF) << 24) |
                    ((bytes[1] & 0xFF) << 16) |
                    ((bytes[2] & 0xFF) << 8) |
                    (bytes[3] & 0xFF);

        return value / 30000.0;  // 旧的错误算法
    }

    
    /**
     * 解析定位数据
     * @param message 消息对象
     * @param content 内容数据
     */
    private void parseLocationData(IotMessage message, byte[] content) {
        if (content == null || content.length < 15) {
            log.warn("[PROTOCOL-DECODE] 定位数据包内容长度不足: {}", content != null ? content.length : 0);
            return;
        }

        try {
            // 解析日期时间
            int year = 2000 + bcdToDec(content[0]);
            int month = bcdToDec(content[1]);
            int day = bcdToDec(content[2]);
            int hour = bcdToDec(content[3]);
            int minute = bcdToDec(content[4]);
            int second = bcdToDec(content[5]);
            LocalDateTime dateTime = LocalDateTime.of(year, month, day, hour, minute, second);

            // 解析卫星数量
            int satellites = content[6] & 0x0F;

            // 解析经纬度
            byte[] latitudeBytes = new byte[4];
            byte[] longitudeBytes = new byte[4];
            System.arraycopy(content, 7, latitudeBytes, 0, 4);
            System.arraycopy(content, 11, longitudeBytes, 0, 4);
            double latitude = parseCoordinate(latitudeBytes);
            double longitude = parseCoordinate(longitudeBytes);

            // 解析速度
            int speed = content[15] & 0xFF;

            // 解析状态和航向
            int status = 0;
            int course = 0;
            if (content.length >= 18) {
                status = content[16] & 0xFF;
                course = content[17] & 0xFF;
            }

            // 解析里程数据（如果数据包包含里程信息）
            Long mileage = null;
            if (content.length >= IotConstants.GT06Protocol.PROTOCOL_LOCATION_WITH_MILEAGE_LENGTH) {
                try {
                    // 里程数据位置：第26-29字节（从第26字节开始的4字节）
                    byte[] mileageBytes = new byte[4];
                    System.arraycopy(content, 26, mileageBytes, 0, 4);

                    // 解析里程数据（大端序，单位：米）
                    long mileageValue = ((mileageBytes[0] & 0xFFL) << 24) |
                                       ((mileageBytes[1] & 0xFFL) << 16) |
                                       ((mileageBytes[2] & 0xFFL) << 8) |
                                       (mileageBytes[3] & 0xFFL);
                    mileage = mileageValue;

                    log.debug("[PROTOCOL-DECODE] 解析里程数据: {}米, 原始数据={}",
                        mileage, bytesToHexString(mileageBytes, 0, 4));

                } catch (Exception mileageException) {
                    log.warn("[PROTOCOL-DECODE] 解析里程数据异常: {}", mileageException.getMessage());
                }
            }

            // 创建GPS数据对象
            GpsData gpsData = GpsData.builder()
                .datetime(dateTime)
                .satellites(satellites)
                .latitude(latitude)
                .longitude(longitude)
                .speed(speed)
                .course(course)
                .status(status)
                .mileage(mileage)
                .build();

            message.setGpsData(gpsData);

            // 更新日志输出，包含里程信息
            if (mileage != null) {
                log.info("[PROTOCOL-DECODE] 解析定位数据(含里程): 时间={}, 经度={}, 纬度={}, 速度={}km/h, 航向={}°, 卫星数={}, 里程={}米",
                    dateTime, longitude, latitude, speed, course, satellites, mileage);
            } else {
                log.info("[PROTOCOL-DECODE] 解析定位数据: 时间={}, 经度={}, 纬度={}, 速度={}km/h, 航向={}°, 卫星数={}",
                    dateTime, longitude, latitude, speed, course, satellites);
            }

            // 解析LBS信息（如果数据包包含LBS信息）
            // 支持两种格式：26字节(标准)和30字节(含里程)
            if (content.length >= IotConstants.GT06Protocol.PROTOCOL_LOCATION_STANDARD_LENGTH) {
                try {
                    // 解析LBS信息（从第18字节开始）
                    LbsData lbsData = LbsData.builder()
                        .mcc((content[18] & 0xFF) << 8 | (content[19] & 0xFF))
                        .mnc(content[20] & 0xFF)
                        .lac((content[21] & 0xFF) << 8 | (content[22] & 0xFF))
                        .cellId((content[23] & 0xFF) << 16 | (content[24] & 0xFF) << 8 | (content[25] & 0xFF))
                        .build();

                    message.setLbsData(lbsData);

                    log.info("[PROTOCOL-DECODE] 解析定位包LBS信息: MCC={}, MNC={}, LAC={}, CellId={}, 数据包类型={}",
                        lbsData.getMcc(), lbsData.getMnc(), lbsData.getLac(), lbsData.getCellId(),
                        content.length >= IotConstants.GT06Protocol.PROTOCOL_LOCATION_WITH_MILEAGE_LENGTH ? "含里程" : "标准");

                } catch (Exception lbsException) {
                    log.warn("[PROTOCOL-DECODE] 解析定位包LBS信息异常: {}, 原始数据={}",
                        lbsException.getMessage(), bytesToHexString(content, 18, Math.min(8, content.length - 18)));
                }
            } else if (content.length > 18) {
                log.debug("[PROTOCOL-DECODE] 定位包长度不足以包含完整LBS信息: 实际长度={}, 需要长度>={}",
                    content.length, IotConstants.GT06Protocol.PROTOCOL_LOCATION_STANDARD_LENGTH);
            }

        } catch (Exception e) {
            log.error("[PROTOCOL-DECODE] 解析定位数据包异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * BCD码转十进制
     * @param bcd BCD编码的字节
     * @return 十进制数值
     */
    private int bcdToDec(byte bcd) {
        return ((bcd & 0xF0) >> 4) * 10 + (bcd & 0x0F);
    }

    /**
     * 解析LBS多基站信息包 (0x18)
     */
    private void parseLbsMultiPacket(IotMessage message, byte[] content) {
        if (content == null || content.length < 9) {
            log.warn("[PROTOCOL-DECODE] LBS多基站信息包内容长度不足: {}", content != null ? content.length : 0);
            return;
        }
        
        try {
            // 解析多基站LBS信息
            LbsData lbsData = LbsData.builder()
                .mcc((content[0] & 0xFF) << 8 | (content[1] & 0xFF))
                .mnc(content[2] & 0xFF)
                .lac((content[3] & 0xFF) << 8 | (content[4] & 0xFF))
                .cellId((content[5] & 0xFF) << 16 | (content[6] & 0xFF) << 8 | (content[7] & 0xFF))
                .build();
            
            message.setLbsData(lbsData);
            log.info("[PROTOCOL-DECODE] 解析LBS多基站信息: MCC={}, MNC={}, LAC={}, CellId={}",
                lbsData.getMcc(), lbsData.getMnc(), lbsData.getLac(), lbsData.getCellId());
            
        } catch (Exception e) {
            log.error("[PROTOCOL-DECODE] 解析LBS多基站信息包异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 解析查询地址信息包 (0x1A)
     */
    private void parseAddressQueryPacket(IotMessage message, byte[] content) {
        if (content == null || content.length < 1) {
            log.warn("[PROTOCOL-DECODE] 查询地址信息包内容长度不足: {}", content != null ? content.length : 0);
            return;
        }
        
        try {
            log.info("[PROTOCOL-DECODE] 解析查询地址信息包: 长度={}", content.length);
            
        } catch (Exception e) {
            log.error("[PROTOCOL-DECODE] 解析查询地址信息包异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 解析LBS+WIFI信息包 (0x2C)
     */
    private void parseLbsWifiPacket(IotMessage message, byte[] content) {
        if (content == null || content.length < 9) {
            log.warn("[PROTOCOL-DECODE] LBS+WIFI信息包内容长度不足: {}", content != null ? content.length : 0);
            return;
        }
        
        try {
            // 解析LBS信息
            LbsData lbsData = LbsData.builder()
                .mcc((content[0] & 0xFF) << 8 | (content[1] & 0xFF))
                .mnc(content[2] & 0xFF)
                .lac((content[3] & 0xFF) << 8 | (content[4] & 0xFF))
                .cellId((content[5] & 0xFF) << 16 | (content[6] & 0xFF) << 8 | (content[7] & 0xFF))
                .build();
            
            message.setLbsData(lbsData);
            
            // 解析WIFI信息（如果有）
            if (content.length > 9) {
                // TODO: 解析WIFI热点信息
                log.info("[PROTOCOL-DECODE] 检测到WIFI信息，长度={}", content.length - 9);
            }
            
            log.info("[PROTOCOL-DECODE] 解析LBS+WIFI信息: MCC={}, MNC={}, LAC={}, CellId={}",
                lbsData.getMcc(), lbsData.getMnc(), lbsData.getLac(), lbsData.getCellId());
            
        } catch (Exception e) {
            log.error("[PROTOCOL-DECODE] 解析LBS+WIFI信息包异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 解析服务器指令下发包 (0x80)
     */
    private void parseServerCommandPacket(IotMessage message, byte[] content) {
        if (content == null || content.length < 1) {
            log.warn("[PROTOCOL-DECODE] 服务器指令下发包内容长度不足: {}", content != null ? content.length : 0);
            return;
        }
        
        try {
            log.info("[PROTOCOL-DECODE] 解析服务器指令下发包: 长度={}", content.length);
            
        } catch (Exception e) {
            log.error("[PROTOCOL-DECODE] 解析服务器指令下发包异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 解析录音协议包 (0x8D)
     */
    private void parseRecordPacket(IotMessage message, byte[] content) {
        if (content == null || content.length < 1) {
            log.warn("[PROTOCOL-DECODE] 录音协议包内容长度不足: {}", content != null ? content.length : 0);
            return;
        }
        
        try {
            log.info("[PROTOCOL-DECODE] 解析录音协议包: 长度={}", content.length);
            
        } catch (Exception e) {
            log.error("[PROTOCOL-DECODE] 解析录音协议包异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 解析IMSI号上报包 (0x90)
     */
    private void parseImsiPacket(IotMessage message, byte[] content) {
        if (content == null || content.length < 8) {
            log.warn("[PROTOCOL-DECODE] IMSI号上报包内容长度不足: {}", content != null ? content.length : 0);
            return;
        }
        
        try {
            StringBuilder imsi = new StringBuilder();
            for (int i = 0; i < Math.min(8, content.length); i++) {
                int high = (content[i] >> 4) & 0x0F;
                int low = content[i] & 0x0F;
                if (high != 0x0F) imsi.append(high);
                if (low != 0x0F) imsi.append(low);
            }
            
            message.setImsi(imsi.toString());
            log.info("[PROTOCOL-DECODE] 解析IMSI号: {}", message.getImsi());
            
        } catch (Exception e) {
            log.error("[PROTOCOL-DECODE] 解析IMSI号上报包异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 解析ICCID号上报包 (0x94)
     */
    private void parseIccidPacket(IotMessage message, byte[] content) {
        if (content == null || content.length < 10) {
            log.warn("[PROTOCOL-DECODE] ICCID号上报包内容长度不足: {}", content != null ? content.length : 0);
            return;
        }
        
        try {
            StringBuilder iccid = new StringBuilder();
            for (int i = 0; i < Math.min(10, content.length); i++) {
                int high = (content[i] >> 4) & 0x0F;
                int low = content[i] & 0x0F;
                if (high != 0x0F) iccid.append(high);
                if (low != 0x0F) iccid.append(low);
            }
            
            message.setIccid(iccid.toString());
            log.info("[PROTOCOL-DECODE] 解析ICCID号: {}", message.getIccid());
            
        } catch (Exception e) {
            log.error("[PROTOCOL-DECODE] 解析ICCID号上报包异常: {}", e.getMessage(), e);
        }
    }
}
