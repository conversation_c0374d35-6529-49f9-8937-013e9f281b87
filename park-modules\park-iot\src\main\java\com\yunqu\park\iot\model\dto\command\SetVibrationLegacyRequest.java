package com.yunqu.park.iot.model.dto.command;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置震动报警请求（兼容指令）
 * 兼容旧版VIBRATION,X,M#指令格式
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetVibrationLegacyRequest extends BaseCommandReq {
    
    /**
     * 报警级别 (0-5)
     * 0为关闭报警，1到5级，数字越低越灵敏，默认0
     */
    @NotNull(message = "报警级别不能为空")
    @Min(value = 0, message = "报警级别不能小于0")
    @Max(value = 5, message = "报警级别不能大于5")
    private Integer level;
    
    /**
     * 报警方式 (0-4)
     * 0：仅GPRS；1：电话+GPRS；2：短信+GPRS；3：电话+短信+GPRS；4：电话+GPRS
     */
    @NotNull(message = "报警方式不能为空")
    @Min(value = 0, message = "报警方式不能小于0")
    @Max(value = 4, message = "报警方式不能大于4")
    private Integer mode;
}
