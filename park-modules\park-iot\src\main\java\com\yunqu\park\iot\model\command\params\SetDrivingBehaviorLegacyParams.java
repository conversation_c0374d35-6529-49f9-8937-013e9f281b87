package com.yunqu.park.iot.model.command.params;

import lombok.Data;

/**
 * 设置驾驶行为报警参数（旧指令）
 * 对应EACCELE,灵敏度#、EDECELE,灵敏度#、EANGLE,灵敏度#指令
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
public class SetDrivingBehaviorLegacyParams {
    
    /**
     * 驾驶行为类型
     * EACCELE: 急加速报警
     * EDECELE: 急减速报警  
     * EANGLE: 急转弯报警
     */
    private String behaviorType;
    
    /**
     * 灵敏度阈值
     * 急加速：2~10m/s²之间可设置，设置成0表示关闭
     * 急减速：3~10m/s²之间可设置，设置成0表示关闭
     * 急转弯：3~15m/s²之间可设置，设置成0表示关闭
     */
    private Integer sensitivity;
}
