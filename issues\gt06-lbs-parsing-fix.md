# GT06协议0x12定位数据包LBS信息解析修复

## 问题描述
终端箱服务器发送的0x12定位数据包中，MCC、MNC、LAC、Cell ID等LBS信息解析为空。

## 问题分析

### 数据包结构（根据用户提供图片）
```
格式          | 长度(Byte) | 示例
起始位        | 2          | 0x78 0x78
包长度        | 1          | 0x1F
协议号        | 1          | 0x12
日期时间      | 6          | 0x13 0x08 0x1D 0x11 0x0C 0x10
GPS信息卫星数 | 1          | 0xCB
纬度          | 4          | 0x02 0x7A 0xCF 0xEB
经度          | 4          | 0x0C 0x46 0x58 0x49
速度          | 1          | 0x10
航向、状态    | 2          | 0x15 0x40
MCC           | 2          | 0x01 0x00
MNC           | 1          | 0x00
LAC           | 2          | 0x28 0x7D
Cell ID       | 3          | 0x00 0x1F 0xB8
序列号        | 2          | 0x00 0x03
错误校验      | 2          | 0x9D 0xDC
结束位        | 2          | 0x0D 0x0A
```

### 根本原因
`parseLocationData`方法只解析了前18字节的GPS数据，完全忽略了第18-26字节的LBS信息。

### 预期解析结果
- MCC: 0x01 0x00 → 256
- MNC: 0x00 → 0
- LAC: 0x28 0x7D → 10365
- Cell ID: 0x00 0x1F 0xB8 → 8120

## 修复方案
在`parseLocationData`方法中添加LBS信息解析逻辑，复用现有的LBS解析代码。

## 实施步骤
1. 修改数据长度检查逻辑
2. 添加LBS信息解析（第18-26字节）
3. 创建LbsData对象并设置到message
4. 添加调试日志

## 风险评估
- 风险等级：低
- 影响范围：仅影响0x12协议的LBS信息解析
- 向后兼容：支持不同长度的数据包
