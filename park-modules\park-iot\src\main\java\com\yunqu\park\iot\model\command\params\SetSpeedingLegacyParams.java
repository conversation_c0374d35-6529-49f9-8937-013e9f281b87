package com.yunqu.park.iot.model.command.params;

import lombok.Data;

/**
 * 设置超速报警参数（兼容指令）
 * 对应SPEEDING,速度,报警方式#指令
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
public class SetSpeedingLegacyParams {
    
    /**
     * 速度设置 (60-220 km/h)
     * 当值不在此范围内，则为关闭报警
     */
    private Integer speed;
    
    /**
     * 报警方式 (0-3)
     * 0：仅GPRS；1：电话+GPRS；2：短信+GPRS；3：电话+短信+GPRS
     */
    private Integer mode;
}
