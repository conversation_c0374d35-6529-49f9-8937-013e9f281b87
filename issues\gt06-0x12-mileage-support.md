# GT06协议0x12定位数据包里程数据兼容

## 需求描述
0x12协议号需要支持带有里程数据的数据包变种，在原有GPS+LBS信息基础上增加4字节里程数据。

## 数据包格式对比

### 标准格式（26字节内容）
```
格式          | 长度(Byte) | 说明
起始位        | 2          | 0x78 0x78
包长度        | 1          | 0x1A (26字节)
协议号        | 1          | 0x12
日期时间      | 6          | BCD编码
GPS信息卫星数 | 1          | 低4位有效
纬度          | 4          | 大端序
经度          | 4          | 大端序
速度          | 1          | km/h
航向、状态    | 2          | 航向+状态位
MCC           | 2          | 移动国家代码
MNC           | 1          | 移动网络代码
LAC           | 2          | 位置区代码
Cell ID       | 3          | 基站ID
序列号        | 2          | 大端序
CRC校验       | 2          | 大端序
结束位        | 2          | 0x0D 0x0A
```

### 带里程格式（30字节内容）
```
格式          | 长度(Byte) | 示例
起始位        | 2          | 0x78 0x78
包长度        | 1          | 0x23 (35字节)
协议号        | 1          | 0x12
日期时间      | 6          | 0x13 0x08 0x1D 0x11 0x0C 0x10
GPS信息卫星数 | 1          | 0xCB
纬度          | 4          | 0x02 0x7A 0xCF 0xEB
经度          | 4          | 0x0C 0x46 0x58 0x49
速度          | 1          | 0x10
航向、状态    | 2          | 0x15 0x40
MCC           | 2          | 0x01 0x00
MNC           | 1          | 0x00
LAC           | 2          | 0x28 0x7D
Cell ID       | 3          | 0x00 0x1F 0xB8
里程          | 4          | 0x00 0x0F 0x42 0x40 (新增)
序列号        | 2          | 0x00 0x03
CRC校验       | 2          | 0x9D 0xDC
结束位        | 2          | 0x0D 0x0A
```

## 实施计划

### 1. 修改GpsData模型
- 添加mileage字段（Long类型，单位：米）
- 支持null值（向后兼容）

### 2. 修改GT06ProtocolDecoder
- 在parseLocationData方法中检测数据包长度
- content.length >= 30时解析里程数据
- 里程数据位置：LBS信息后的4字节（大端序）

### 3. 向后兼容
- 26字节格式：mileage字段为null
- 30字节格式：解析里程数据

## 技术细节
- 里程数据：4字节大端序整数，单位为米
- 数据位置：第26-29字节（从0开始计数）
- 解析公式：((byte[0] & 0xFF) << 24) | ((byte[1] & 0xFF) << 16) | ((byte[2] & 0xFF) << 8) | (byte[3] & 0xFF)

## 实施完成情况

### ✅ 已完成的修改

1. **GpsData模型扩展**
   - 添加了`mileage`字段（Long类型，支持null）
   - 保持向后兼容性

2. **GT06ProtocolDecoder解码器增强**
   - 修改了`parseLocationData`方法
   - 支持检测30字节数据包并解析里程数据
   - 添加了详细的日志输出
   - 使用常量定义提高代码可维护性

3. **IotConstants常量定义**
   - 添加了`PROTOCOL_LOCATION_STANDARD_LENGTH = 26`
   - 添加了`PROTOCOL_LOCATION_WITH_MILEAGE_LENGTH = 30`

4. **IotProtocolServiceImpl数据存储**
   - 重构了`parseAndSaveLocationData`方法
   - 使用已解析的GpsData和LbsData对象
   - 支持里程数据存储到数据库

5. **数据库支持**
   - 确认数据库表`iot_location_data`已包含`mileage`字段
   - 确认实体类`IotLocationData`已支持里程数据
   - 确认Mapper XML已包含里程字段的插入和查询

### ✅ 测试验证

1. **单元测试**
   - 创建了`GT06MileageTest`测试类
   - 验证标准26字节和含里程30字节数据包解析
   - 验证里程数据的正确解析

2. **集成测试**
   - 创建了验证程序测试完整流程
   - 验证数据转换逻辑
   - 确认从协议解析到数据库存储的完整链路

### ✅ 兼容性保证

- **向后兼容**：26字节标准格式仍正常工作，mileage字段为null
- **向前兼容**：30字节含里程格式正确解析并存储里程数据
- **数据完整性**：所有原有字段解析逻辑保持不变

## 使用示例

### 标准数据包（26字节）
```
包长度: 0x1A (26字节)
内容: GPS(18字节) + LBS(8字节)
结果: mileage = null
```

### 含里程数据包（30字节）
```
包长度: 0x23 (35字节，包含序列号等)
内容: GPS(18字节) + LBS(8字节) + 里程(4字节)
结果: mileage = 解析的里程值（米）
```

## 验证方法

1. 发送标准0x12数据包，确认正常解析且里程为null
2. 发送含里程0x12数据包，确认里程数据正确解析和存储
3. 查询数据库确认里程字段正确保存
