package com.yunqu.park.iot.model.dto.command;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置震动灵敏度级别请求
 * 用于静止时震动唤醒工作的灵敏度设置
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetSensorLevelRequest extends BaseCommandReq {
    
    /**
     * 震动灵敏度级别 (1-10)
     * 等级数值越小越灵敏，默认级别5
     */
    @NotNull(message = "震动灵敏度级别不能为空")
    @Min(value = 1, message = "震动灵敏度级别不能小于1")
    @Max(value = 10, message = "震动灵敏度级别不能大于10")
    private Integer level;
}
