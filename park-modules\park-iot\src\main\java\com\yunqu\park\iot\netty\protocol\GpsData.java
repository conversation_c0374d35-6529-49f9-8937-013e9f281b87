package com.yunqu.park.iot.netty.protocol;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * GPS定位数据模型
 * 用于封装从协议号 0x12, 0x16 等包中解析出的定位信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GpsData {

    /**
     * 日期时间
     */
    private LocalDateTime datetime;

    /**
     * GPS卫星数量
     */
    private int satellites;

    /**
     * 纬度
     */
    private double latitude;

    /**
     * 经度
     */
    private double longitude;

    /**
     * 速度 (km/h)
     */
    private int speed;

    /**
     * 航向 (0-359度，正北为0)
     */
    private int course;

    /**
     * 状态位原始值
     * bit0: 0-未定位, 1-已定位
     * bit1: 0-北纬, 1-南纬
     * bit2: 0-东经, 1-西经
     * bit10: 0-GPS实时, 1-差分定位
     * bit11: 0-车辆未设防, 1-车辆设防
     */
    private int status;

    /**
     * 里程数据 (单位：米)
     * 仅在带里程的0x12协议包中存在，标准包中为null
     */
    private Long mileage;

    // 可以根据status位添加一些便捷的boolean方法
    public boolean isPositioning() {
        return (status & 0x01) == 1;
    }

    public boolean isGpsRealTime() {
        return (status & (1 << 10)) == 0;
    }

    public boolean isVehicleArmed() {
        return (status & (1 << 11)) == 1;
    }
}