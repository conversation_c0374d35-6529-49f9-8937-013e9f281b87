package com.yunqu.park.iot.model.dto.command;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置驾驶行为报警请求（旧指令）
 * 兼容旧版EACCELE,灵敏度#等指令格式
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetDrivingBehaviorLegacyRequest extends BaseCommandReq {
    
    /**
     * 驾驶行为类型
     * EACCELE: 急加速报警
     * EDECELE: 急减速报警  
     * EANGLE: 急转弯报警
     */
    @NotNull(message = "驾驶行为类型不能为空")
    private String behaviorType;
    
    /**
     * 灵敏度阈值
     * 急加速：2~10m/s²之间可设置，设置成0表示关闭
     * 急减速：3~10m/s²之间可设置，设置成0表示关闭
     * 急转弯：3~15m/s²之间可设置，设置成0表示关闭
     */
    @NotNull(message = "灵敏度阈值不能为空")
    @Min(value = 0, message = "灵敏度阈值不能小于0")
    @Max(value = 25, message = "灵敏度阈值不能大于25")
    private Integer sensitivity;
}
