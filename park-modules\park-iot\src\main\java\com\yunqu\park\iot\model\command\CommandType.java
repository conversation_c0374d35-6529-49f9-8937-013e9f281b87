package com.yunqu.park.iot.model.command;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 定义了所有可用的设备指令类型.
 * 该枚举是基于R12L-F指令表生成的.
 */
@Getter
@RequiredArgsConstructor
public enum CommandType {

    // --- 基础指令 ---
    SET_IP("设置IP"),
    SET_DUAL_IP("设置双IP"),
    SET_APN("设置APN"),
    SET_UPLOAD_INTERVAL("定时上传间隔"),
    SET_HEARTBEAT_INTERVAL("心跳包间隔"),
    SET_SOS_NUMBER("设置SOS号码"),
    DELETE_SOS_NUMBER("删除SOS号码"),
    SET_CENTER_NUMBER("设置中心号码"),
    DELETE_CENTER_NUMBER("删除中心号码"),
    CONTROL_RELAY("控制油电"),
    SET_TIMEZONE("时区设置"),
    SET_LANGUAGE("语言设置"),
    SET_AUTO_ARM("自动设防"),
    SET_DISARM_STATE("撤防后设备状态"),
    SET_ANGLE_UPLOAD("拐点补传开关"),
    SET_ANGLE_VALUE("拐点角度设置"),
    CLEAR_DATA("清除各种数据"),
    SET_UPLOAD_TIMEZONE("上传数据时区"),
    REBOOT("重启"),
    FACTORY_RESET("恢复出厂"),

    // --- 报警类指令 ---
    SET_VIBRATION_SENSITIVITY("震动报警灵敏度级别设置"),
    SET_SENSOR_LEVEL("震动灵敏度级别设置"),
    SET_VIBRATION_ALARM("震动报警"),
    SET_VIBRATION_LEGACY("震动报警（兼容指令）"),
    SET_POWER_ALARM("断电报警"),
    SET_LOW_BATTERY_ALARM("低电报警"),
    SET_SPEED_ALARM("速度报警"),
    SET_SPEEDING_LEGACY("超速报警（兼容指令）"),
    SET_MOVING_ALARM("位移报警"),
    SET_ACC_ALARM("ACC报警"),
    SET_HARSH_ACCELERATION_ALARM("急加速报警"),
    SET_HARSH_BRAKING_ALARM("急减速报警"),
    SET_HARSH_TURNING_ALARM("急转弯报警"),
    SET_COLLISION_ALARM("碰撞报警"),
    SET_DRIVING_BEHAVIOR_LEGACY("驾驶行为报警（旧指令）"),

    // --- 查询类指令 ---
    QUERY_LOCATION("查询定位"),
    QUERY_LINK_ADDRESS("查询链路地址"),
    QUERY_PARAMETERS("查询参数"),
    QUERY_STATUS("查询状态"),
    QUERY_VERSION("查询版本"),
    QUERY_ICCID("查询ICCID"),
    QUERY_NETWORK("查询网络"),
    QUERY_MODULE_VERSION("查询模块版本"),
    QUERY_VIBRATION_SENSITIVITY("查询震动报警灵敏度"),
    QUERY_SENSOR_LEVEL("查询震动灵敏度级别"),
    QUERY_MILEAGE_STATISTICS("里程统计查询"),
    QUERY_NETWORK_MODE("查询网络模式"),

    // --- 功能扩展类指令 ---
    CLEAR_MILEAGE("里程清零"),
    SWITCH_MILEAGE_MODE("里程模式切换"),
    SET_MILEAGE_STATISTICS("里程统计"),
    SET_STATIC_SLEEP("静止休眠开关"),
    SET_SPEED_MEASUREMENT_MODE("测速模式设置"),
    SET_GPS_MODE("GPS模式设置"),
    SET_GPS_UPLOAD_DURATION("GPS上传时长"),
    SET_GPRS_SWITCH("GPRS开关"),
    SET_SATELLITE_LOCK_SWITCH("锁星开关"),
    SET_DISTANCE_UPLOAD("定距上传定位包"),
    SET_GPS_SLEEP_WORK("GPS休眠工作"),
    SET_STATIC_UPLOAD_INTERVAL("静止上传间隔");

    private final String description;
}