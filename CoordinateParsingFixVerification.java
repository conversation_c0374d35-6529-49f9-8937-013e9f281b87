/**
 * GPS坐标解析修复验证程序
 */
public class CoordinateParsingFixVerification {
    
    public static void main(String[] args) {
        System.out.println("=== GPS坐标解析修复验证 ===\n");
        
        // 测试新的定位数据包
        testNewLocationPacket();
        
        System.out.println("\n" + "=".repeat(80) + "\n");
        
        // 测试原始定位数据包
        testOriginalLocationPacket();
        
        System.out.println("\n" + "=".repeat(80) + "\n");
        
        // 对比分析
        compareParsingMethods();
    }
    
    private static void testNewLocationPacket() {
        System.out.println("=== 新定位数据包测试 ===");
        
        // 新数据包的经纬度部分：02 7C 59 33 0C 2B 90 20
        byte[] latitudeBytes = {0x02, 0x7C, 0x59, 0x33};
        byte[] longitudeBytes = {0x0C, 0x2B, (byte)0x90, 0x20};
        
        System.out.println("原始数据包: 78 78 1F 12 19 08 06 16 03 2E CD 02 7C 59 33 0C 2B 90 20 00 D5 22 01 CC 00 25 03 00 0B 11 0A 73 3D 6C 0D 0A");
        System.out.println("纬度字节: " + bytesToHexString(latitudeBytes));
        System.out.println("经度字节: " + bytesToHexString(longitudeBytes));
        System.out.println();
        
        // 修复前的解析
        double oldLatitude = parseCoordinateOldWay(latitudeBytes);
        double oldLongitude = parseCoordinateOldWay(longitudeBytes);
        
        // 修复后的解析
        double newLatitude = parseCoordinateNewWay(latitudeBytes);
        double newLongitude = parseCoordinateNewWay(longitudeBytes);
        
        System.out.println("修复前解析结果:");
        System.out.println("  纬度: " + oldLatitude + "°");
        System.out.println("  经度: " + oldLongitude + "°");
        System.out.println();
        
        System.out.println("修复后解析结果:");
        System.out.println("  纬度: " + newLatitude + "°");
        System.out.println("  经度: " + newLongitude + "°");
        System.out.println();
        
        System.out.println("期望结果:");
        System.out.println("  纬度: N23.16899°");
        System.out.println("  经度: E113.43476°");
        System.out.println();
        
        // 验证结果
        double expectedLat = 23.16899;
        double expectedLng = 113.43476;
        double latDiff = Math.abs(newLatitude - expectedLat);
        double lngDiff = Math.abs(newLongitude - expectedLng);
        
        System.out.println("精度验证:");
        System.out.println("  纬度误差: " + String.format("%.6f°", latDiff) + 
                          (latDiff < 0.001 ? " ✅ 精度良好" : " ⚠️ 精度需要改进"));
        System.out.println("  经度误差: " + String.format("%.6f°", lngDiff) + 
                          (lngDiff < 0.001 ? " ✅ 精度良好" : " ⚠️ 精度需要改进"));
    }
    
    private static void testOriginalLocationPacket() {
        System.out.println("=== 原始定位数据包测试 ===");
        
        // 原始数据包的经纬度部分：02 7C 59 33 0C 2B 90 20
        byte[] latitudeBytes = {0x02, 0x7C, 0x59, 0x33};
        byte[] longitudeBytes = {0x0C, 0x2B, (byte)0x90, 0x20};
        
        System.out.println("原始数据包: 78 78 1F 12 19 08 06 13 37 1E CE 02 7C 59 33 0C 2B 90 20 00 D5 22 01 CC 00 25 03 00 0B 11 07 82 28 65 0D 0A");
        System.out.println("纬度字节: " + bytesToHexString(latitudeBytes));
        System.out.println("经度字节: " + bytesToHexString(longitudeBytes));
        System.out.println();
        
        // 修复前后的解析对比
        double oldLatitude = parseCoordinateOldWay(latitudeBytes);
        double oldLongitude = parseCoordinateOldWay(longitudeBytes);
        double newLatitude = parseCoordinateNewWay(latitudeBytes);
        double newLongitude = parseCoordinateNewWay(longitudeBytes);
        
        System.out.println("解析结果对比:");
        System.out.println("  修复前 - 纬度: " + oldLatitude + "°, 经度: " + oldLongitude + "°");
        System.out.println("  修复后 - 纬度: " + newLatitude + "°, 经度: " + newLongitude + "°");
        System.out.println();
        
        System.out.println("坐标合理性检查:");
        System.out.println("  修复前坐标是否合理: " + (isValidCoordinate(oldLatitude, oldLongitude) ? "✅ 是" : "❌ 否"));
        System.out.println("  修复后坐标是否合理: " + (isValidCoordinate(newLatitude, newLongitude) ? "✅ 是" : "❌ 否"));
    }
    
    private static void compareParsingMethods() {
        System.out.println("=== 解析方法对比分析 ===");
        
        System.out.println("修复前算法 (错误):");
        System.out.println("  degrees = value / 30000.0");
        System.out.println("  问题: 只进行了单位转换，没有从分转换为度");
        System.out.println();
        
        System.out.println("修复后算法 (正确):");
        System.out.println("  degrees = value / 1800000.0");
        System.out.println("  原理: 1800000 = 30000 * 60");
        System.out.println("  步骤: 原始值 → 分(÷30000) → 度(÷60)");
        System.out.println();
        
        System.out.println("GT06协议坐标存储格式:");
        System.out.println("  - 坐标以'分'为单位存储");
        System.out.println("  - 需要先转换为分，再转换为度");
        System.out.println("  - 1度 = 60分");
        System.out.println();
        
        // 示例计算
        byte[] exampleBytes = {0x02, 0x7C, 0x59, 0x33};
        int value = ((exampleBytes[0] & 0xFF) << 24) |
                    ((exampleBytes[1] & 0xFF) << 16) |
                    ((exampleBytes[2] & 0xFF) << 8) |
                    (exampleBytes[3] & 0xFF);
        
        System.out.println("示例计算 (纬度 02 7C 59 33):");
        System.out.println("  原始值: " + value);
        System.out.println("  修复前: " + value + " ÷ 30000 = " + (value / 30000.0) + "°");
        System.out.println("  修复后: " + value + " ÷ 1800000 = " + (value / 1800000.0) + "°");
        System.out.println("  分步计算: " + value + " ÷ 30000 = " + (value / 30000.0) + "分");
        System.out.println("           " + (value / 30000.0) + " ÷ 60 = " + ((value / 30000.0) / 60.0) + "°");
    }
    
    /**
     * 修复前的坐标解析 (错误的)
     */
    private static double parseCoordinateOldWay(byte[] bytes) {
        int value = ((bytes[0] & 0xFF) << 24) |
                    ((bytes[1] & 0xFF) << 16) |
                    ((bytes[2] & 0xFF) << 8) |
                    (bytes[3] & 0xFF);
        return value / 30000.0;
    }
    
    /**
     * 修复后的坐标解析 (正确的)
     */
    private static double parseCoordinateNewWay(byte[] bytes) {
        int value = ((bytes[0] & 0xFF) << 24) |
                    ((bytes[1] & 0xFF) << 16) |
                    ((bytes[2] & 0xFF) << 8) |
                    (bytes[3] & 0xFF);
        return value / 1800000.0;
    }
    
    /**
     * 检查坐标是否在合理范围内
     */
    private static boolean isValidCoordinate(double latitude, double longitude) {
        return latitude >= -90 && latitude <= 90 && 
               longitude >= -180 && longitude <= 180;
    }
    
    /**
     * 字节数组转十六进制字符串
     */
    private static String bytesToHexString(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            if (i > 0) sb.append(" ");
            sb.append(String.format("%02X", bytes[i] & 0xFF));
        }
        return sb.toString();
    }
}
