package com.yunqu.park.iot.netty.codec;

import com.yunqu.park.iot.netty.protocol.IotMessage;
import com.yunqu.park.iot.netty.protocol.LbsData;
import com.yunqu.park.iot.netty.protocol.GpsData;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

/**
 * GT06协议LBS信息解析测试
 * 验证0x12定位数据包中LBS信息的正确解析
 */
public class GT06LbsParsingTest {

    private GT06ProtocolDecoder decoder;

    @BeforeEach
    void setUp() {
        decoder = new GT06ProtocolDecoder();
    }

    /**
     * 测试用户提供的实际数据包
     * 数据包结构：78 78 1F 12 13 08 1D 11 0C 10 CB 02 7A CF EB 0C 46 58 49 10 15 40 01 00 00 28 7D 00 1F B8 00 03 9D DC 0D 0A
     */
    @Test
    void testRealLocationPacketWithLbs() {
        // 构造测试数据包（去除起始位、长度、协议号、序列号、CRC、结束位）
        byte[] content = {
            // 日期时间 (6字节): 2025-08-29 17:12:16
            0x13, 0x08, 0x1D, 0x11, 0x0C, 0x10,
            // GPS信息卫星数 (1字节): 11颗卫星
            (byte) 0xCB,
            // 纬度 (4字节): 0x027ACFEB
            0x02, 0x7A, (byte) 0xCF, (byte) 0xEB,
            // 经度 (4字节): 0x0C465849  
            0x0C, 0x46, 0x58, 0x49,
            // 速度 (1字节): 16 km/h
            0x10,
            // 航向、状态 (2字节)
            0x15, 0x40,
            // LBS信息 (8字节)
            // MCC (2字节): 0x0100 = 256
            0x01, 0x00,
            // MNC (1字节): 0x00 = 0
            0x00,
            // LAC (2字节): 0x287D = 10365
            0x28, 0x7D,
            // Cell ID (3字节): 0x001FB8 = 8120
            0x00, 0x1F, (byte) 0xB8
        };

        // 创建消息对象
        IotMessage message = new IotMessage();
        
        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = GT06ProtocolDecoder.class.getDeclaredMethod("parseLocationData", IotMessage.class, byte[].class);
            method.setAccessible(true);
            method.invoke(decoder, message, content);
            
            // 验证GPS数据解析
            GpsData gpsData = message.getGpsData();
            assertNotNull(gpsData, "GPS数据应该被正确解析");
            assertEquals(11, gpsData.getSatellites(), "卫星数量应该为11");
            assertEquals(16, gpsData.getSpeed(), "速度应该为16 km/h");
            
            // 验证LBS数据解析
            LbsData lbsData = message.getLbsData();
            assertNotNull(lbsData, "LBS数据应该被正确解析");
            assertEquals(256, lbsData.getMcc(), "MCC应该为256");
            assertEquals(0, lbsData.getMnc(), "MNC应该为0");
            assertEquals(10365, lbsData.getLac(), "LAC应该为10365");
            assertEquals(8120, lbsData.getCellId(), "Cell ID应该为8120");
            
            System.out.println("✅ LBS解析测试通过:");
            System.out.println("   MCC: " + lbsData.getMcc());
            System.out.println("   MNC: " + lbsData.getMnc());
            System.out.println("   LAC: " + lbsData.getLac());
            System.out.println("   Cell ID: " + lbsData.getCellId());
            
        } catch (Exception e) {
            fail("解析过程中发生异常: " + e.getMessage());
        }
    }

    /**
     * 测试只包含GPS数据的数据包（向后兼容性测试）
     */
    @Test
    void testGpsOnlyPacket() {
        // 构造只包含GPS数据的测试包（18字节）
        byte[] content = {
            // 日期时间 (6字节)
            0x13, 0x08, 0x1D, 0x11, 0x0C, 0x10,
            // GPS信息卫星数 (1字节)
            (byte) 0xCB,
            // 纬度 (4字节)
            0x02, 0x7A, (byte) 0xCF, (byte) 0xEB,
            // 经度 (4字节)
            0x0C, 0x46, 0x58, 0x49,
            // 速度 (1字节)
            0x10,
            // 航向、状态 (2字节)
            0x15, 0x40
        };

        IotMessage message = new IotMessage();
        
        try {
            java.lang.reflect.Method method = GT06ProtocolDecoder.class.getDeclaredMethod("parseLocationData", IotMessage.class, byte[].class);
            method.setAccessible(true);
            method.invoke(decoder, message, content);
            
            // 验证GPS数据正常解析
            GpsData gpsData = message.getGpsData();
            assertNotNull(gpsData, "GPS数据应该被正确解析");
            
            // 验证LBS数据为空（向后兼容）
            LbsData lbsData = message.getLbsData();
            assertNull(lbsData, "LBS数据应该为空（数据包不包含LBS信息）");
            
            System.out.println("✅ 向后兼容性测试通过: GPS数据正常解析，LBS数据为空");
            
        } catch (Exception e) {
            fail("解析过程中发生异常: " + e.getMessage());
        }
    }

    /**
     * 测试数据包长度不足的情况
     */
    @Test
    void testInsufficientDataLength() {
        // 构造长度不足的数据包
        byte[] content = {0x13, 0x08, 0x1D}; // 只有3字节

        IotMessage message = new IotMessage();
        
        try {
            java.lang.reflect.Method method = GT06ProtocolDecoder.class.getDeclaredMethod("parseLocationData", IotMessage.class, byte[].class);
            method.setAccessible(true);
            method.invoke(decoder, message, content);
            
            // 验证数据未被解析
            assertNull(message.getGpsData(), "GPS数据应该为空（数据长度不足）");
            assertNull(message.getLbsData(), "LBS数据应该为空（数据长度不足）");
            
            System.out.println("✅ 数据长度不足测试通过: 正确处理了长度不足的情况");
            
        } catch (Exception e) {
            fail("解析过程中发生异常: " + e.getMessage());
        }
    }
}
